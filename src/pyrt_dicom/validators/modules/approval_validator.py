"""Approval Module Validator - DICOM PS3.3 C.8.8.16 validation."""

from __future__ import annotations

from pydicom import Dataset
from typing import TYPE_CHECKING
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult
from ...enums.approval_enums import ApprovalStatus

if TYPE_CHECKING:
    from ...modules.base_module import BaseModule


class ApprovalValidator(BaseValidator):
    """Validator for Approval Module requirements."""
    
    @staticmethod
    def validate(dataset: 'Dataset | BaseModule', config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Approval Module requirements on any pydicom Dataset or BaseModule.
        
        Args:
            dataset: pydicom Dataset or BaseModule instance to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Validate Type 1 requirements
        ApprovalValidator._validate_required_elements(dataset, result)
        
        # Validate Type 2C conditional requirements
        if config.validate_conditional_requirements:
            ApprovalValidator._validate_conditional_requirements(dataset, result)
        
        # Validate enumerated values
        if config.check_enumerated_values:
            ApprovalValidator._validate_enumerated_values(dataset, result)
        
        # Validate date/time formats
        ApprovalValidator._validate_date_time_formats(dataset, result)
        
        return result
    
    @staticmethod
    def _validate_required_elements(dataset: 'Dataset | BaseModule', result: ValidationResult) -> None:
        """Validate Type 1 required elements."""
        if 'ApprovalStatus' not in dataset:
            result.add_error(
                "Missing required Type 1 element: ApprovalStatus (300E,0002). "
                "This element is mandatory for all Approval Module instances per DICOM PS3.3 C.8.8.16."
            )
    
    @staticmethod
    def _validate_conditional_requirements(dataset: 'Dataset | BaseModule', result: ValidationResult) -> None:
        """Validate Type 2C conditional requirements."""
        approval_status = dataset.get('ApprovalStatus', '')
        if not approval_status:
            return
        
        requires_review_info = approval_status in [ApprovalStatus.APPROVED.value, ApprovalStatus.REJECTED.value]
        
        if requires_review_info:
            # Review Date, Review Time, and Reviewer Name are required (Type 2C)
            required_review_elements = {
                'ReviewDate': '(300E,0004)',
                'ReviewTime': '(300E,0005)', 
                'ReviewerName': '(300E,0008)'
            }
            
            missing_elements = []
            for field_name, tag in required_review_elements.items():
                if field_name not in dataset:
                    missing_elements.append(f"{field_name} {tag}")
            
            if missing_elements:
                result.add_error(
                    f"Missing required Type 2C elements for ApprovalStatus '{approval_status}': "
                    f"{', '.join(missing_elements)}. Per DICOM PS3.3 C.8.8.16, review information "
                    f"is required when ApprovalStatus is APPROVED or REJECTED."
                )
        else:
            # For UNAPPROVED status, review information should not be present
            if approval_status == ApprovalStatus.UNAPPROVED.value:
                review_fields = ['ReviewDate', 'ReviewTime', 'ReviewerName']
                present_fields = [field for field in review_fields if field in dataset]
                
                if present_fields:
                    # Map field names to DICOM tags for clearer messaging
                    field_tags = {
                        'ReviewDate': '(300E,0004)',
                        'ReviewTime': '(300E,0005)',
                        'ReviewerName': '(300E,0008)'
                    }
                    tagged_fields = [f"{field} {field_tags[field]}" for field in present_fields]
                    
                    result.add_error(
                        f"Type 2C elements present for UNAPPROVED status: {', '.join(tagged_fields)}. "
                        f"Per DICOM PS3.3 C.8.8.16, review information should not be present "
                        f"when ApprovalStatus is UNAPPROVED."
                    )
    
    @staticmethod
    def _validate_enumerated_values(dataset: 'Dataset | BaseModule', result: ValidationResult) -> None:
        """Validate enumerated values."""
        if 'ApprovalStatus' in dataset:
            allowed_values = [e.value for e in ApprovalStatus]
            BaseValidator.validate_enumerated_value(
                dataset['ApprovalStatus'].value, allowed_values, 
                'ApprovalStatus (300E,0002)', result
            )
    
    @staticmethod
    def _validate_date_time_formats(dataset: 'Dataset | BaseModule', result: ValidationResult) -> None:
        """Validate date and time format requirements."""
        # Validate Review Date format (YYYYMMDD) - DICOM VR: DA
        if 'ReviewDate' in dataset:
            if not BaseValidator.validate_date_format(dataset['ReviewDate']):
                result.add_error(
                    f"ReviewDate (300E,0004) value '{dataset['ReviewDate']}' is invalid. "
                    f"Must be in DICOM DA format (YYYYMMDD) per DICOM PS3.5."
                )
        
        # Validate Review Time format (HHMMSS or HHMMSS.FFFFFF) - DICOM VR: TM
        if 'ReviewTime' in dataset:
            if not BaseValidator.validate_time_format(dataset['ReviewTime']):
                result.add_error(
                    f"ReviewTime (300E,0005) value '{dataset['ReviewTime']}' is invalid. "
                    f"Must be in DICOM TM format (HHMMSS or HHMMSS.FFFFFF) per DICOM PS3.5."
                )
    