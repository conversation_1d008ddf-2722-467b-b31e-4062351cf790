"""Common Instance Reference Module DICOM validation - PS3.3 C.12.2

This validator ensures 100% compliance with DICOM PS3.3 C.12.2 Common Instance Reference Module
specifications, including proper validation of hierarchical reference structures, conditional
requirements, and embedded macro implementations.

Key Validation Areas:
- Type 1C conditional sequence requirements
- SOP Instance Reference Macro (Table 10-11) compliance
- Series and Instance Reference Macro (Table 10-4) compliance
- Hierarchical reference structure integrity
- Cross-reference consistency validation
"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class CommonInstanceReferenceValidator(BaseValidator):
    """Validator for DICOM Common Instance Reference Module (PS3.3 C.12.2).

    Provides comprehensive validation of hierarchical SOP Instance references
    within DICOM datasets, ensuring full compliance with DICOM standard
    requirements for cross-study and same-study instance references.

    Validation Coverage:
    - Type 1C conditional requirements for reference sequences
    - SOP Instance Reference Macro (Table 10-11) validation
    - Series and Instance Reference Macro (Table 10-4) validation
    - Sequence structure and cardinality requirements
    - Reference hierarchy consistency checks
    """
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Common Instance Reference Module requirements on any pydicom Dataset.

        Performs comprehensive validation of DICOM PS3.3 C.12.2 Common Instance Reference Module
        requirements, including conditional logic, sequence structures, and macro implementations.

        Args:
            dataset: pydicom Dataset to validate against DICOM standard requirements
            config: Optional validation configuration to control validation behavior

        Returns:
            ValidationResult: Comprehensive validation result with specific error messages
                and actionable guidance for resolving any DICOM compliance issues
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()

        # Validate Type 1C conditional requirements for reference sequences
        if config.validate_conditional_requirements:
            CommonInstanceReferenceValidator._validate_conditional_requirements(dataset, result)

        # Validate sequence structures and Type 1 element requirements
        if config.validate_sequences:
            CommonInstanceReferenceValidator._validate_sequence_requirements(dataset, result)

        # Validate SOP Instance Reference Macro (Table 10-11) requirements
        if config.validate_sequences:
            CommonInstanceReferenceValidator._validate_sop_instance_reference_macro(dataset, result)

        # Validate reference hierarchy consistency
        if config.validate_sequences:
            CommonInstanceReferenceValidator._validate_reference_hierarchy(dataset, result)

        return result
    
    @staticmethod
    def _validate_conditional_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate Type 1C conditional requirements for reference sequences.

        DICOM PS3.3 C.12.2 specifies two Type 1C sequences:
        - Referenced Series Sequence (0008,1115): Required if this Instance references Instances in this Study
        - Studies Containing Other Referenced Instances Sequence (0008,1200): Required if this Instance references Instances in other Studies

        Validation Logic:
        - Both sequences can be present (references to same study AND other studies)
        - Only one sequence can be present (references to either same study OR other studies)
        - Neither sequence is required if no instances are referenced
        - Empty sequences are considered invalid if present
        """
        # Check for empty sequences (present but with no items)
        if hasattr(dataset, 'ReferencedSeriesSequence') and len(getattr(dataset, 'ReferencedSeriesSequence', [])) == 0:
            result.add_warning(
                "Referenced Series Sequence (0008,1115) is present but empty. "
                "If no same-study instances are referenced, this sequence should be absent. "
                "If instances are referenced, at least one item must be included per DICOM PS3.3 C.12.2."
            )

        if hasattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence') and len(getattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence', [])) == 0:
            result.add_warning(
                "Studies Containing Other Referenced Instances Sequence (0008,1200) is present but empty. "
                "If no cross-study instances are referenced, this sequence should be absent. "
                "If instances are referenced, at least one item must be included per DICOM PS3.3 C.12.2."
            )

        # Valid states: both present, one present, or neither present
        # No additional validation needed as the conditional logic is context-dependent
    
    @staticmethod
    def _validate_sequence_requirements(dataset: Dataset, result: ValidationResult) -> None:
        """Validate sequence structure and Type 1 element requirements.

        Validates the hierarchical structure of reference sequences according to
        DICOM PS3.3 C.12.2 and embedded macro requirements.
        """

        # Validate Referenced Series Sequence structure (same-study references)
        ref_series_seq = getattr(dataset, 'ReferencedSeriesSequence', [])
        for i, series_item in enumerate(ref_series_seq):
            # Series Instance UID (0020,000E) is Type 1 in Referenced Series Sequence
            if not series_item.get('SeriesInstanceUID'):
                result.add_error(
                    f"Referenced Series Sequence item {i}: Series Instance UID (0020,000E) is required (Type 1). "
                    f"Each series reference must include a unique series identifier to establish proper "
                    f"hierarchical relationships per DICOM PS3.3 C.12.2. "
                    f"Solution: Set SeriesInstanceUID to a valid DICOM UID."
                )
            elif not CommonInstanceReferenceValidator._is_valid_uid(series_item.get('SeriesInstanceUID')):
                result.add_error(
                    f"Referenced Series Sequence item {i}: Series Instance UID (0020,000E) '{series_item.get('SeriesInstanceUID')}' "
                    f"is not a valid DICOM UID format. UIDs must follow ISO 8824 standard with numeric components "
                    f"separated by periods. Solution: Use a properly formatted DICOM UID."
                )

            # Referenced Instance Sequence (0008,114A) is Type 1 within Referenced Series Sequence
            if 'ReferencedInstanceSequence' not in series_item:
                result.add_error(
                    f"Referenced Series Sequence item {i}: Referenced Instance Sequence (0008,114A) is required (Type 1). "
                    f"Each series reference must contain at least one instance reference to be meaningful "
                    f"per DICOM PS3.3 C.12.2. Solution: Add at least one referenced instance item."
                )
            elif len(series_item.get('ReferencedInstanceSequence', [])) == 0:
                result.add_error(
                    f"Referenced Series Sequence item {i}: Referenced Instance Sequence (0008,114A) is empty. "
                    f"DICOM PS3.3 C.12.2 requires 'One or more Items shall be included in this Sequence'. "
                    f"Solution: Add at least one referenced instance item or remove the empty series reference."
                )

        # Validate Studies Containing Other Referenced Instances Sequence structure (cross-study references)
        other_studies_seq = getattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence', [])
        for i, study_item in enumerate(other_studies_seq):
            # Study Instance UID (0020,000D) is Type 1 in Studies Containing Other Referenced Instances Sequence
            if not study_item.get('StudyInstanceUID'):
                result.add_error(
                    f"Studies Containing Other Referenced Instances Sequence item {i}: "
                    f"Study Instance UID (0020,000D) is required (Type 1). "
                    f"Each cross-study reference must include a unique study identifier to establish proper "
                    f"hierarchical relationships per DICOM PS3.3 C.12.2. "
                    f"Solution: Set StudyInstanceUID to a valid DICOM UID."
                )
            elif not CommonInstanceReferenceValidator._is_valid_uid(study_item.get('StudyInstanceUID')):
                result.add_error(
                    f"Studies Containing Other Referenced Instances Sequence item {i}: "
                    f"Study Instance UID (0020,000D) '{study_item.get('StudyInstanceUID')}' "
                    f"is not a valid DICOM UID format. Solution: Use a properly formatted DICOM UID."
                )

            # Validate Series and Instance Reference Macro (Table 10-4) implementation
            CommonInstanceReferenceValidator._validate_series_instance_reference_macro(study_item, i, result)
    
    @staticmethod
    def _validate_sop_instance_reference_macro(dataset: Dataset, result: ValidationResult) -> None:
        """Validate SOP Instance Reference Macro (Table 10-11) requirements within sequences.

        Validates all referenced instance items according to DICOM PS3.3 Table 10-11
        SOP Instance Reference Macro specifications.
        """

        def validate_referenced_instance_item(item: Dataset, location: str) -> None:
            """Validate a single referenced instance item against Table 10-11 requirements."""
            # Referenced SOP Class UID (0008,1150) - Type 1
            if not item.get('ReferencedSOPClassUID'):
                result.add_error(
                    f"{location}: Referenced SOP Class UID (0008,1150) is required (Type 1). "
                    f"This attribute uniquely identifies the referenced SOP Class per DICOM PS3.3 Table 10-11. "
                    f"Solution: Set ReferencedSOPClassUID to a valid DICOM SOP Class UID."
                )
            elif not CommonInstanceReferenceValidator._is_valid_uid(item.get('ReferencedSOPClassUID')):
                result.add_error(
                    f"{location}: Referenced SOP Class UID (0008,1150) '{item.get('ReferencedSOPClassUID')}' "
                    f"is not a valid DICOM UID format. Solution: Use a properly formatted DICOM SOP Class UID."
                )

            # Referenced SOP Instance UID (0008,1155) - Type 1
            if not item.get('ReferencedSOPInstanceUID'):
                result.add_error(
                    f"{location}: Referenced SOP Instance UID (0008,1155) is required (Type 1). "
                    f"This attribute uniquely identifies the referenced SOP Instance per DICOM PS3.3 Table 10-11. "
                    f"Solution: Set ReferencedSOPInstanceUID to a valid DICOM SOP Instance UID."
                )
            elif not CommonInstanceReferenceValidator._is_valid_uid(item.get('ReferencedSOPInstanceUID')):
                result.add_error(
                    f"{location}: Referenced SOP Instance UID (0008,1155) '{item.get('ReferencedSOPInstanceUID')}' "
                    f"is not a valid DICOM UID format. Solution: Use a properly formatted DICOM SOP Instance UID."
                )

            # Referenced Frame Number (0008,1160) - Type 1C validation
            # Note: Full validation requires external context about whether the referenced SOP Instance is multi-frame
            if 'ReferencedFrameNumber' in item:
                frame_numbers = item.get('ReferencedFrameNumber', [])
                if not isinstance(frame_numbers, (list, tuple)) or len(frame_numbers) == 0:
                    result.add_warning(
                        f"{location}: Referenced Frame Number (0008,1160) is present but empty or invalid. "
                        f"If specified, it must contain at least one frame number per DICOM PS3.3 Table 10-11."
                    )

            # Referenced Segment Number (0062,000B) - Type 1C validation
            # Note: Full validation requires external context about whether the referenced SOP Instance is a Segmentation
            if 'ReferencedSegmentNumber' in item:
                segment_numbers = item.get('ReferencedSegmentNumber', [])
                if not isinstance(segment_numbers, (list, tuple)) or len(segment_numbers) == 0:
                    result.add_warning(
                        f"{location}: Referenced Segment Number (0062,000B) is present but empty or invalid. "
                        f"If specified, it must contain at least one segment number per DICOM PS3.3 Table 10-11."
                    )

        # Validate Referenced Series Sequence items (same-study references)
        ref_series_seq = getattr(dataset, 'ReferencedSeriesSequence', [])
        for i, series_item in enumerate(ref_series_seq):
            ref_instance_seq = series_item.get('ReferencedInstanceSequence', [])
            for j, instance_item in enumerate(ref_instance_seq):
                location = f"Referenced Series Sequence item {i}, Referenced Instance Sequence item {j}"
                validate_referenced_instance_item(instance_item, location)

        # Validate Studies Containing Other Referenced Instances Sequence items (cross-study references)
        other_studies_seq = getattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence', [])
        for i, study_item in enumerate(other_studies_seq):
            if 'ReferencedSeriesSequence' in study_item:
                ref_series_seq = study_item.get('ReferencedSeriesSequence', [])
                for j, series_item in enumerate(ref_series_seq):
                    if 'ReferencedInstanceSequence' in series_item:
                        ref_instance_seq = series_item.get('ReferencedInstanceSequence', [])
                        for k, instance_item in enumerate(ref_instance_seq):
                            location = (f"Studies Containing Other Referenced Instances Sequence item {i}, "
                                       f"Referenced Series Sequence item {j}, Referenced Instance Sequence item {k}")
                            validate_referenced_instance_item(instance_item, location)

    @staticmethod
    def _validate_series_instance_reference_macro(study_item: Dataset, study_index: int, result: ValidationResult) -> None:
        """Validate Series and Instance Reference Macro (Table 10-4) within study items.

        Args:
            study_item: Study item from Studies Containing Other Referenced Instances Sequence
            study_index: Index of the study item for error reporting
            result: ValidationResult to update with any issues found
        """
        if 'ReferencedSeriesSequence' in study_item:
            ref_series_seq = study_item.get('ReferencedSeriesSequence', [])
            if len(ref_series_seq) == 0:
                result.add_warning(
                    f"Studies Containing Other Referenced Instances Sequence item {study_index}: "
                    f"Referenced Series Sequence is present but empty. If no series are referenced, "
                    f"this sequence should be absent per DICOM PS3.3 Table 10-4."
                )

            for j, series_item in enumerate(ref_series_seq):
                # Series Instance UID (0020,000E) - Type 1 in Table 10-4
                if not series_item.get('SeriesInstanceUID'):
                    result.add_error(
                        f"Studies Containing Other Referenced Instances Sequence item {study_index}, "
                        f"Referenced Series Sequence item {j}: Series Instance UID (0020,000E) is required (Type 1). "
                        f"Each series reference must include a unique series identifier per DICOM PS3.3 Table 10-4. "
                        f"Solution: Set SeriesInstanceUID to a valid DICOM UID."
                    )
                elif not CommonInstanceReferenceValidator._is_valid_uid(series_item.get('SeriesInstanceUID')):
                    result.add_error(
                        f"Studies Containing Other Referenced Instances Sequence item {study_index}, "
                        f"Referenced Series Sequence item {j}: Series Instance UID (0020,000E) "
                        f"'{series_item.get('SeriesInstanceUID')}' is not a valid DICOM UID format."
                    )

                # Referenced Instance Sequence (0008,114A) - Type 1 in Table 10-4
                if 'ReferencedInstanceSequence' not in series_item:
                    result.add_error(
                        f"Studies Containing Other Referenced Instances Sequence item {study_index}, "
                        f"Referenced Series Sequence item {j}: Referenced Instance Sequence (0008,114A) is required (Type 1). "
                        f"Each series reference must contain at least one instance reference per DICOM PS3.3 Table 10-4. "
                        f"Solution: Add at least one referenced instance item."
                    )
                elif len(series_item.get('ReferencedInstanceSequence', [])) == 0:
                    result.add_error(
                        f"Studies Containing Other Referenced Instances Sequence item {study_index}, "
                        f"Referenced Series Sequence item {j}: Referenced Instance Sequence (0008,114A) is empty. "
                        f"DICOM PS3.3 Table 10-4 requires 'One or more Items shall be included in this Sequence'. "
                        f"Solution: Add at least one referenced instance item."
                    )

    @staticmethod
    def _validate_reference_hierarchy(dataset: Dataset, result: ValidationResult) -> None:
        """Validate reference hierarchy consistency and completeness.

        Performs additional validation to ensure the hierarchical reference structure
        is logically consistent and complete according to DICOM requirements.
        """
        # Count total references for consistency checking
        total_same_study_instances = 0
        total_cross_study_instances = 0

        # Count same-study references
        ref_series_seq = getattr(dataset, 'ReferencedSeriesSequence', [])
        for series_item in ref_series_seq:
            ref_instance_seq = series_item.get('ReferencedInstanceSequence', [])
            total_same_study_instances += len(ref_instance_seq)

        # Count cross-study references
        other_studies_seq = getattr(dataset, 'StudiesContainingOtherReferencedInstancesSequence', [])
        for study_item in other_studies_seq:
            if 'ReferencedSeriesSequence' in study_item:
                ref_series_seq = study_item.get('ReferencedSeriesSequence', [])
                for series_item in ref_series_seq:
                    ref_instance_seq = series_item.get('ReferencedInstanceSequence', [])
                    total_cross_study_instances += len(ref_instance_seq)

        # Provide informational summary
        total_references = total_same_study_instances + total_cross_study_instances
        if total_references == 0:
            result.add_warning(
                "Common Instance Reference Module contains no instance references. "
                "If this module is present, it typically indicates that instance references should be included. "
                "Consider whether this module is necessary if no references are being made."
            )

    @staticmethod
    def _is_valid_uid(uid: str) -> bool:
        """Validate DICOM UID format according to ISO 8824 standard.

        Args:
            uid: UID string to validate

        Returns:
            bool: True if UID format is valid, False otherwise
        """
        if not uid or not isinstance(uid, str):
            return False

        # DICOM UIDs must be composed of numeric components separated by periods
        # Maximum length is 64 characters
        if len(uid) > 64:
            return False

        # Must contain only digits and periods
        if not all(c.isdigit() or c == '.' for c in uid):
            return False

        # Must not start or end with a period
        if uid.startswith('.') or uid.endswith('.'):
            return False

        # Must not contain consecutive periods
        if '..' in uid:
            return False

        # Each component must be numeric and not start with 0 (except for single digit 0)
        components = uid.split('.')
        for component in components:
            if not component:  # Empty component
                return False
            if len(component) > 1 and component.startswith('0'):  # Leading zeros not allowed
                return False
            if not component.isdigit():  # Must be numeric
                return False

        return True
