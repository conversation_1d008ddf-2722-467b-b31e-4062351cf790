"""Clinical Trial Series Module DICOM validation - PS3.3 C.7.3.2"""

from pydicom import Dataset
from .base_validator import BaseValidator, ValidationConfig
from ..validation_result import ValidationResult


class ClinicalTrialSeriesValidator(BaseValidator):
    """Validator for DICOM Clinical Trial Series Module (PS3.3 C.7.3.2)."""
    
    @staticmethod
    def validate(dataset: Dataset, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate Clinical Trial Series Module requirements on any pydicom Dataset.
        
        Args:
            dataset: pydicom Dataset to validate
            config: Validation configuration options
            
        Returns:
            ValidationResult with errors and warnings
        """
        config = config or ValidationConfig()
        result = BaseValidator.create_validation_result()
        
        # Check Type 2 requirements - must be present (can be empty)
        if not hasattr(dataset, 'ClinicalTrialCoordinatingCenterName'):
            result.add_error(
                "Clinical Trial Coordinating Center Name (0012,0060) is required (Type 2). "
                "Must be present but may have zero-length value."
            )
        
        # Validate DICOM VR constraints for LO (Long String) data type
        # All elements in this module are VR=LO with VM=1
        _validate_lo_element(result, dataset, 'ClinicalTrialCoordinatingCenterName', '(0012,0060)')
        _validate_lo_element(result, dataset, 'ClinicalTrialSeriesID', '(0012,0071)')
        _validate_lo_element(result, dataset, 'IssuerOfClinicalTrialSeriesID', '(0012,0073)')
        _validate_lo_element(result, dataset, 'ClinicalTrialSeriesDescription', '(0012,0072)')
        
        # Semantic validation: If Series ID is provided, recommend issuer for better identification
        if (hasattr(dataset, 'ClinicalTrialSeriesID') and 
            dataset.ClinicalTrialSeriesID and 
            str(dataset.ClinicalTrialSeriesID).strip() and
            (not hasattr(dataset, 'IssuerOfClinicalTrialSeriesID') or 
             not dataset.IssuerOfClinicalTrialSeriesID)):
            result.add_warning(
                "Clinical Trial Series ID (0012,0071) is present but Issuer of Clinical Trial Series ID (0012,0073) "
                "is not provided. Consider adding issuer information for better series identification."
            )
        
        return result


def _validate_lo_element(result: ValidationResult, dataset: Dataset, element_name: str, tag: str) -> None:
    """Validate Long String (LO) VR constraints for clinical trial series elements.
    
    LO VR constraints:
    - Maximum length: 64 characters
    - Character set: Default Character Set and extensions (typically ISO 8859-1)
    - Leading/trailing spaces should be insignificant
    
    Args:
        result: ValidationResult to add errors/warnings to
        dataset: Dataset to validate
        element_name: Name of the DICOM element
        tag: DICOM tag in format (gggg,eeee)
    """
    if hasattr(dataset, element_name):
        value = getattr(dataset, element_name)
        if value is not None:
            # Convert to string for validation
            str_value = str(value) if value is not None else ""
            
            # Check maximum length for LO VR (64 characters)
            if len(str_value) > 64:
                result.add_error(
                    f"{element_name} {tag} exceeds maximum length of 64 characters for LO VR. "
                    f"Current length: {len(str_value)} characters."
                )
            
            # Check for control characters (except space)
            if any(ord(char) < 32 for char in str_value if char != ' '):
                result.add_error(
                    f"{element_name} {tag} contains invalid control characters. "
                    f"LO VR should not contain control characters except space."
                )
