"""
Approval Module - DICOM PS3.3 C.8.8.16

The Approval Module contains attributes that describe the approval status
of a DICOM object at the time the SOP Instance was created.
"""
from datetime import datetime, date
from .base_module import BaseModule
from ..enums.approval_enums import ApprovalStatus
from ..validators import ValidationResult
from ..validators.modules.approval_validator import ApprovalValidator
from ..validators.modules.base_validator import ValidationConfig
from ..utils.dicom_formatters import format_date_value, format_time_value, format_enum_string


class ApprovalModule(BaseModule):
    """Approval Module implementation for DICOM PS3.3 C.8.8.16.
    
    Contains attributes that describe the approval status of a DICOM object
    at the time the SOP Instance was created.
    
    Usage:
        # Create with approved status
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240101",    # YYYYMMDD format
            review_time="120000",      # HHMMSS format
            reviewer_name="<PERSON><PERSON> <PERSON>"  # DICOM Person Name format
        )
        
        # Create with rejected status (also requires review information)
        rejected = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.REJECTED
        ).with_review_information(
            review_date="20240102",
            review_time="093000",
            reviewer_name="Dr. Jones"
        )
        
        # Create with unapproved status (no review info needed or allowed)
        unapproved = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.UNAPPROVED  # Review info not allowed for UNAPPROVED
        )
        
        # Enum usage provides runtime validation and type safety
        assert approval.is_approved  # Boolean property from enum
        assert rejected.is_rejected  
        assert unapproved.is_unapproved
        
        # Check if review information is required based on status
        if approval.requires_review_information:
            print("Review information is required")
        
        # Generate dataset for IOD integration  
        dataset = approval.to_dataset()
        
        # Validate with comprehensive results
        result = approval.validate()
    """
    
    @classmethod
    def from_required_elements(
        cls,
        approval_status: ApprovalStatus | str
    ) -> 'ApprovalModule':
        """Create module with required elements.
        
        Args:
            approval_status (ApprovalStatus | str): Approval status at the time the SOP Instance was created (300E,0002) Type 1.
                Use ApprovalStatus enum for type safety: ApprovalStatus.APPROVED, ApprovalStatus.UNAPPROVED, ApprovalStatus.REJECTED.
            
        Returns:
            ApprovalModule: New module instance with required data elements set
        """
        instance = cls()
        instance["ApprovalStatus"] = format_enum_string(approval_status)
        return instance
    
    def with_review_information(
        self,
        review_date: str | date | datetime,
        review_time: str | datetime,
        reviewer_name: str
    ) -> 'ApprovalModule':
        """Add review information (Type 2C).
        
        Required if Approval Status (300E,0002) is APPROVED or REJECTED.
        Should not be present if Approval Status is UNAPPROVED.
        
        Args:
            review_date: Date on which object was reviewed (300E,0004) - DICOM DA format
            review_time: Time at which object was reviewed (300E,0005) - DICOM TM format  
            reviewer_name: Name of person who reviewed object (300E,0008) - DICOM PN format
            
        Returns:
            ApprovalModule: Self for method chaining
            
        Raises:
            ValueError: If trying to add review information for UNAPPROVED status
        """
        # Validate conditional requirement before setting
        if 'ApprovalStatus' in self:
            approval_status = self.ApprovalStatus
            if approval_status == ApprovalStatus.UNAPPROVED.value:
                raise ValueError(
                    "Review information should not be provided for UNAPPROVED status. "
                    "Type 2C elements ReviewDate, ReviewTime, and ReviewerName are only "
                    "required when ApprovalStatus is APPROVED or REJECTED."
                )
        
        self.ReviewDate = format_date_value(review_date)
        self.ReviewTime = format_time_value(review_time)
        self.ReviewerName = reviewer_name
        return self
    
    def with_optional_elements(
        self
    ) -> 'ApprovalModule':
        """Add optional (Type 3) elements.
        
        Note: The core DICOM PS3.3 C.8.8.16 Approval Module contains only:
        - Type 1: ApprovalStatus (required)
        - Type 2C: ReviewDate, ReviewTime, ReviewerName (conditional)
        
        There are no Type 3 (optional) elements defined in this module.
        This method is provided for API consistency with other modules.
        
        Returns:
            ApprovalModule: Self for method chaining
        """
        # No Type 3 elements defined in DICOM PS3.3 C.8.8.16 Approval Module
        return self
    
    @property
    def is_approved(self) -> bool:
        """Check if object is approved.
        
        Returns:
            bool: True if approval status is APPROVED
        """
        return 'ApprovalStatus' in self and self.ApprovalStatus == ApprovalStatus.APPROVED.value
    
    @property
    def is_rejected(self) -> bool:
        """Check if object is rejected.
        
        Returns:
            bool: True if approval status is REJECTED
        """
        return 'ApprovalStatus' in self and self.ApprovalStatus == ApprovalStatus.REJECTED.value
    
    @property
    def is_unapproved(self) -> bool:
        """Check if object is unapproved.
        
        Returns:
            bool: True if approval status is UNAPPROVED
        """
        return 'ApprovalStatus' in self and self.ApprovalStatus == ApprovalStatus.UNAPPROVED.value
    
    @property
    def requires_review_information(self) -> bool:
        """Check if review information is required.
        
        Returns:
            bool: True if approval status requires review information
        """
        return self.is_approved or self.is_rejected
    
    @property
    def has_review_information(self) -> bool:
        """Check if review information is present.
        
        Returns:
            bool: True if all review information fields are present
        """
        return ('ReviewDate' in self and 
                'ReviewTime' in self and 
                'ReviewerName' in self)
    
    @property
    def is_configured(self) -> bool:
        """Check if module is properly configured.
        
        Returns:
            bool: True if approval status is present
        """
        return 'ApprovalStatus' in self
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Approval Module instance.
        
        Args:
            config: Optional validation configuration
            
        Returns:
            ValidationResult with 'errors' and 'warnings' lists
        """
        return ApprovalValidator.validate(self, config)
