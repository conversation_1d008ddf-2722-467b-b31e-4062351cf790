"""Clinical Trial Series Module - DICOM PS3.3 C.7.3.2

The Clinical Trial Series Module contains attributes that identify a Series 
in the context of a clinical trial or research.
"""
from .base_module import BaseModule
from ..validators.modules.base_validator import ValidationConfig
from ..validators.modules.clinical_trial_series_validator import ClinicalTrialSeriesValidator
from pyrt_dicom.validators import ValidationResult


class ClinicalTrialSeriesModule(BaseModule):
    """Clinical Trial Series Module implementation for DICOM PS3.3 C.7.3.2.
    
    Inherits from BaseModule to provide native DICOM data handling.
    Contains attributes that identify a Series in the context of a clinical trial or research.
    
    Usage:
        # Create with required elements (Type 2: coordinating center name required but can be empty)
        trial_series = ClinicalTrialSeriesModule.from_required_elements(
            clinical_trial_coordinating_center_name="Research Center"  # Type 2, or "" if unknown
        )
        
        # Add optional elements
        trial_series.with_optional_elements(
            clinical_trial_series_id="SERIES001",
            issuer_of_clinical_trial_series_id="ISSUER001", 
            clinical_trial_series_description="Baseline CT imaging"
        )
        
        # Validate
        result = trial_series.validate()
    """

    @classmethod
    def from_required_elements(
        cls,
        clinical_trial_coordinating_center_name: str = ""
    ) -> 'ClinicalTrialSeriesModule':
        """Create ClinicalTrialSeriesModule from all required (Type 1 and Type 2) data elements.
        
        Args:
            clinical_trial_coordinating_center_name (str): Name of the institution responsible for 
                coordinating the medical imaging data for the clinical trial or research (0012,0060) Type 2
        
        Returns:
            ClinicalTrialSeriesModule: New dataset instance with required elements
        """
        instance = cls()
        instance.ClinicalTrialCoordinatingCenterName = clinical_trial_coordinating_center_name
        return instance
    
    def with_optional_elements(
        self,
        clinical_trial_series_id: str | None = None,
        issuer_of_clinical_trial_series_id: str | None = None,
        clinical_trial_series_description: str | None = None
    ) -> 'ClinicalTrialSeriesModule':
        """Add optional (Type 3) data elements without conditional requirements.
        
        Args:
            clinical_trial_series_id (str | None): Identifier for the Series (0012,0071) Type 3
            issuer_of_clinical_trial_series_id (str | None): Assigning Authority for series ID (0012,0073) Type 3
            clinical_trial_series_description (str | None): Description of the Series (0012,0072) Type 3
            
        Returns:
            ClinicalTrialSeriesModule: Self with optional elements added
        """
        if clinical_trial_series_id is not None:
            self.ClinicalTrialSeriesID = clinical_trial_series_id
        if issuer_of_clinical_trial_series_id is not None:
            self.IssuerOfClinicalTrialSeriesID = issuer_of_clinical_trial_series_id
        if clinical_trial_series_description is not None:
            self.ClinicalTrialSeriesDescription = clinical_trial_series_description
        return self
    
    @property
    def has_coordinating_center_info(self) -> bool:
        """Check if coordinating center information is present and non-empty.
        
        Returns:
            bool: True if coordinating center name is present and has a value
        """
        return 'ClinicalTrialCoordinatingCenterName' in self and bool(self.ClinicalTrialCoordinatingCenterName)
    
    @property
    def has_series_identification(self) -> bool:
        """Check if series identification information is present.
        
        Returns:
            bool: True if series ID or description is present
        """
        return any(attr in self for attr in [
            'ClinicalTrialSeriesID', 'ClinicalTrialSeriesDescription'
        ])
    
    @property
    def has_issuer_info(self) -> bool:
        """Check if issuer information is present.
        
        Returns:
            bool: True if issuer of series ID is present
        """
        return 'IssuerOfClinicalTrialSeriesID' in self
    
    def validate(self, config: ValidationConfig | None = None) -> ValidationResult:
        """Validate this Clinical Trial Series Module instance.
        
        Args:
            config (ValidationConfig | None): Optional validation configuration
        Returns:
            ValidationResult: Validation result object
        """
        return ClinicalTrialSeriesValidator.validate(self._dataset, config)
