"""
Test ClinicalTrialSubjectValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.7.1.3 Clinical Trial Subject Module validation.
"""

import pytest
from pydicom import Dataset
from pyrt_dicom.validators.modules.clinical_trial_subject_validator import ClinicalTrialSubjectValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult


class TestClinicalTrialSubjectValidator:
    """Test ClinicalTrialSubjectValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_empty_dataset_fails_validation(self):
        """Test that empty dataset fails validation (missing required Type 1 elements)."""
        dataset = Dataset()
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have errors for missing Type 1 elements
        assert any("Clinical Trial Sponsor Name (0012,0010)" in error for error in result.errors)
        assert any("Clinical Trial Protocol ID (0012,0020)" in error for error in result.errors)
    
    def test_valid_minimal_dataset_passes_validation(self):
        """Test that dataset with all required elements passes validation."""
        dataset = Dataset()
        # Type 1 elements
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        # Type 2 elements
        dataset.ClinicalTrialProtocolName = "Phase II Cancer Study"
        dataset.ClinicalTrialSiteID = "SITE-001"
        dataset.ClinicalTrialSiteName = "University Medical Center"
        # Type 1C - at least one subject identification required
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_missing_type_1_sponsor_name_generates_error(self):
        """Test that missing Clinical Trial Sponsor Name (Type 1) generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Clinical Trial Sponsor Name (0012,0010) is required (Type 1)" in error for error in result.errors)
    
    def test_empty_type_1_sponsor_name_generates_error(self):
        """Test that empty Clinical Trial Sponsor Name (Type 1) generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = ""  # Empty string not allowed for Type 1
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Clinical Trial Sponsor Name (0012,0010) is required (Type 1)" in error for error in result.errors)
    
    def test_missing_type_1_protocol_id_generates_error(self):
        """Test that missing Clinical Trial Protocol ID (Type 1) generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Clinical Trial Protocol ID (0012,0020) is required (Type 1)" in error for error in result.errors)
    
    def test_empty_type_1_protocol_id_generates_error(self):
        """Test that empty Clinical Trial Protocol ID (Type 1) generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = ""  # Empty string not allowed for Type 1
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Clinical Trial Protocol ID (0012,0020) is required (Type 1)" in error for error in result.errors)
    
    def test_missing_type_2_protocol_name_generates_error(self):
        """Test that missing Clinical Trial Protocol Name (Type 2) generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        # Missing ClinicalTrialProtocolName (Type 2)
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Clinical Trial Protocol Name (0012,0021) must be present (Type 2)" in error for error in result.errors)
    
    def test_empty_type_2_protocol_name_passes_validation(self):
        """Test that empty Clinical Trial Protocol Name (Type 2) passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""  # Empty string allowed for Type 2
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
    
    def test_missing_type_2_site_id_generates_error(self):
        """Test that missing Clinical Trial Site ID (Type 2) generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        # Missing ClinicalTrialSiteID (Type 2)
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Clinical Trial Site ID (0012,0030) must be present (Type 2)" in error for error in result.errors)
    
    def test_missing_type_2_site_name_generates_error(self):
        """Test that missing Clinical Trial Site Name (Type 2) generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        # Missing ClinicalTrialSiteName (Type 2)
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Clinical Trial Site Name (0012,0031) must be present (Type 2)" in error for error in result.errors)
    
    def test_subject_id_without_reading_id_passes_validation(self):
        """Test that having only Clinical Trial Subject ID (Type 1C) passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"  # Type 1C present
        # ClinicalTrialSubjectReadingID absent
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
    
    def test_reading_id_without_subject_id_passes_validation(self):
        """Test that having only Clinical Trial Subject Reading ID (Type 1C) passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        # ClinicalTrialSubjectID absent
        dataset.ClinicalTrialSubjectReadingID = "READ-12345"  # Type 1C present
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
    
    def test_both_subject_ids_present_passes_validation(self):
        """Test that having both subject IDs (Type 1C) passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"  # Type 1C present
        dataset.ClinicalTrialSubjectReadingID = "READ-12345"  # Type 1C present
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
    
    def test_missing_both_subject_ids_generates_error(self):
        """Test that missing both subject IDs (Type 1C) generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        # Both ClinicalTrialSubjectID and ClinicalTrialSubjectReadingID absent
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Either Clinical Trial Subject ID (0012,0040) or" in error and "is required (Type 1C)" in error for error in result.errors)
    
    def test_empty_both_subject_ids_generates_error(self):
        """Test that empty both subject IDs (Type 1C) generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = ""  # Empty string
        dataset.ClinicalTrialSubjectReadingID = ""  # Empty string
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Either Clinical Trial Subject ID (0012,0040) or" in error and "is required (Type 1C)" in error for error in result.errors)
    
    def test_ethics_committee_name_without_approval_number_passes_validation(self):
        """Test that ethics committee name without approval number passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        dataset.ClinicalTrialProtocolEthicsCommitteeName = "IRB Committee"  # Type 1C present
        # ClinicalTrialProtocolEthicsCommitteeApprovalNumber absent
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
    
    def test_ethics_committee_approval_number_without_name_generates_error(self):
        """Test that ethics committee approval number without name (Type 1C) generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        # ClinicalTrialProtocolEthicsCommitteeName absent
        dataset.ClinicalTrialProtocolEthicsCommitteeApprovalNumber = "IRB-2024-001"  # Present but missing required name
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Clinical Trial Protocol Ethics Committee Name (0012,0081) is required (Type 1C)" in error for error in result.errors)
    
    def test_ethics_committee_both_present_passes_validation(self):
        """Test that both ethics committee name and approval number pass validation."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        dataset.ClinicalTrialProtocolEthicsCommitteeName = "IRB Committee"  # Type 1C present
        dataset.ClinicalTrialProtocolEthicsCommitteeApprovalNumber = "IRB-2024-001"  # Type 3 present
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
    
    def test_valid_other_protocol_ids_sequence_passes_validation(self):
        """Test that valid Other Clinical Trial Protocol IDs Sequence passes validation."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        # Valid sequence with proper Dataset items
        seq_item1 = Dataset()
        seq_item1.ClinicalTrialProtocolID = "PROTOCOL-001"
        seq_item1.IssuerOfClinicalTrialProtocolID = "FDA"
        
        seq_item2 = Dataset()
        seq_item2.ClinicalTrialProtocolID = "PROTOCOL-002"
        seq_item2.IssuerOfClinicalTrialProtocolID = "NCI"
        
        dataset.OtherClinicalTrialProtocolIDsSequence = [seq_item1, seq_item2]
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
    
    def test_sequence_item_missing_protocol_id_generates_error(self):
        """Test that sequence item missing protocol ID generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        # Invalid sequence item missing protocol ID
        seq_item = Dataset()
        seq_item.IssuerOfClinicalTrialProtocolID = "FDA"
        # Missing ClinicalTrialProtocolID
        
        dataset.OtherClinicalTrialProtocolIDsSequence = [seq_item]
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("item 0" in error and "Clinical Trial Protocol ID (0012,0020) is required (Type 1)" in error for error in result.errors)
    
    def test_sequence_item_missing_issuer_generates_error(self):
        """Test that sequence item missing issuer generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        # Invalid sequence item missing issuer
        seq_item = Dataset()
        seq_item.ClinicalTrialProtocolID = "PROTOCOL-001"
        # Missing IssuerOfClinicalTrialProtocolID
        
        dataset.OtherClinicalTrialProtocolIDsSequence = [seq_item]
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("item 0" in error and "Issuer of Clinical Trial Protocol ID (0012,0022) is required (Type 1)" in error for error in result.errors)
    
    def test_sequence_item_not_dataset_generates_error(self):
        """Test that sequence item that is not a Dataset generates error."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        # Test that pydicom itself prevents non-Dataset objects in sequences
        with pytest.raises(TypeError, match="Sequence contents must be 'Dataset' instances"):
            dataset.OtherClinicalTrialProtocolIDsSequence = [
                {"ClinicalTrialProtocolID": "PROTOCOL-001", "IssuerOfClinicalTrialProtocolID": "FDA"}
            ]
    
    def test_validation_with_default_config(self):
        """Test validation behavior with default ValidationConfig."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        # Test with None config (should use defaults)
        result = ClinicalTrialSubjectValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
    
    def test_validation_with_strict_mode_disabled(self):
        """Test validation behavior with strict mode disabled."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        config = ValidationConfig(strict_mode=False)
        result = ClinicalTrialSubjectValidator.validate(dataset, config)
        
        # Should validate normally with strict_mode=False
        assert result.is_valid
    
    def test_validation_with_conditional_requirements_disabled(self):
        """Test validation behavior with conditional requirements disabled."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        # Missing both subject IDs - should be ignored with disabled config
        
        config = ValidationConfig(validate_conditional_requirements=False)
        result = ClinicalTrialSubjectValidator.validate(dataset, config)
        
        # Should not check conditional requirements
        assert not result.has_errors or not any("Type 1C" in error for error in result.errors)
    
    def test_validation_with_sequences_disabled(self):
        """Test validation behavior with sequence validation disabled."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "ACME Research Corp"
        dataset.ClinicalTrialProtocolID = "TRIAL-2024-001"
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "SUBJ-12345"
        
        # Invalid sequence item - missing required fields but should be ignored with disabled config
        seq_item = Dataset()
        # Missing both ClinicalTrialProtocolID and IssuerOfClinicalTrialProtocolID
        dataset.OtherClinicalTrialProtocolIDsSequence = [seq_item]
        
        config = ValidationConfig(validate_sequences=False)
        result = ClinicalTrialSubjectValidator.validate(dataset, config)
        
        # Should not check sequences
        assert not result.has_errors or not any("Sequence" in error for error in result.errors)
    
    def test_error_messages_include_dicom_tag_references(self):
        """Test that error messages include specific DICOM tag references."""
        dataset = Dataset()
        # Missing required elements
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert result.has_errors
        # Check that DICOM tags are included in error messages
        assert any("(0012,0010)" in error for error in result.errors)  # Sponsor Name
        assert any("(0012,0020)" in error for error in result.errors)  # Protocol ID
    
    def test_error_messages_include_dicom_ps3_references(self):
        """Test that error messages include DICOM PS3.3 references."""
        dataset = Dataset()
        # Missing required elements
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert result.has_errors
        # Check that DICOM PS3.3 references are included
        assert any("DICOM PS3.3 C.7.1.3" in error for error in result.errors)
    
    def test_multiple_validation_issues_are_all_reported(self):
        """Test that multiple validation issues are all captured and reported."""
        dataset = Dataset()
        # Missing Type 1 sponsor name
        # Missing Type 1 protocol ID
        # Missing Type 2 protocol name
        # Missing Type 2 site ID  
        # Missing Type 2 site name
        # Missing both subject IDs (Type 1C)
        # Invalid sequence with missing required fields
        seq_item = Dataset()
        # Missing both ClinicalTrialProtocolID and IssuerOfClinicalTrialProtocolID
        dataset.OtherClinicalTrialProtocolIDsSequence = [seq_item]
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        # Should have multiple issues reported
        assert result.has_errors
        assert len(result.errors) >= 7  # At least 7 errors expected (6 from missing fields + 2 from invalid sequence)
    
    def test_whitespace_only_values_treated_as_empty(self):
        """Test that whitespace-only values are treated as empty for validation."""
        dataset = Dataset()
        dataset.ClinicalTrialSponsorName = "   "  # Whitespace only - should fail Type 1
        dataset.ClinicalTrialProtocolID = "\t\n"  # Whitespace only - should fail Type 1
        dataset.ClinicalTrialProtocolName = ""
        dataset.ClinicalTrialSiteID = ""
        dataset.ClinicalTrialSiteName = ""
        dataset.ClinicalTrialSubjectID = "  "  # Whitespace only - should fail Type 1C
        dataset.ClinicalTrialSubjectReadingID = "\t"  # Whitespace only - should fail Type 1C
        
        result = ClinicalTrialSubjectValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        # Should have errors for Type 1 and Type 1C elements
        assert any("Clinical Trial Sponsor Name" in error for error in result.errors)
        assert any("Clinical Trial Protocol ID" in error for error in result.errors)
        assert any("Either Clinical Trial Subject ID" in error for error in result.errors)