"""
Test ApprovalValidator functionality and DICOM compliance.

Tests for DICOM PS3.3 C.8.8.16 Approval Module validation.
"""

from pydicom import Dataset
from pyrt_dicom.validators.modules.approval_validator import ApprovalValidator
from pyrt_dicom.validators.modules.base_validator import ValidationConfig
from pyrt_dicom.validators import ValidationResult
from pyrt_dicom.enums.approval_enums import ApprovalStatus


class TestApprovalValidator:
    """Test ApprovalValidator functionality and DICOM compliance."""
    
    def test_validate_returns_validation_result(self):
        """Test that validate method returns proper ValidationResult instance."""
        dataset = Dataset()
        
        result = ApprovalValidator.validate(dataset)
        
        assert isinstance(result, ValidationResult)
        assert hasattr(result, 'errors')
        assert hasattr(result, 'warnings')
        assert isinstance(result.errors, list)
        assert isinstance(result.warnings, list)
    
    def test_empty_dataset_fails_validation(self):
        """Test that empty dataset fails validation (missing required ApprovalStatus)."""
        dataset = Dataset()
        
        result = ApprovalValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert len(result.errors) == 1
        assert "Missing required Type 1 element: ApprovalStatus (300E,0002)" in result.errors[0]
    
    def test_valid_approved_status_with_review_info_passes_validation(self):
        """Test that valid APPROVED status with review information passes validation."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240101"
        dataset.ReviewTime = "120000"
        dataset.ReviewerName = "Dr. Smith"
        
        result = ApprovalValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_rejected_status_with_review_info_passes_validation(self):
        """Test that valid REJECTED status with review information passes validation."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.REJECTED.value
        dataset.ReviewDate = "20240101"
        dataset.ReviewTime = "120000"
        dataset.ReviewerName = "Dr. Smith"
        
        result = ApprovalValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_valid_unapproved_status_without_review_info_passes_validation(self):
        """Test that valid UNAPPROVED status without review information passes validation."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.UNAPPROVED.value
        
        result = ApprovalValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_approved_status_missing_review_date_generates_error(self):
        """Test that APPROVED status missing ReviewDate generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewTime = "120000"
        dataset.ReviewerName = "Dr. Smith"
        # Missing ReviewDate
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Missing required Type 2C elements" in error and "ReviewDate (300E,0004)" in error for error in result.errors)
    
    def test_approved_status_missing_review_time_generates_error(self):
        """Test that APPROVED status missing ReviewTime generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240101"
        dataset.ReviewerName = "Dr. Smith"
        # Missing ReviewTime
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Missing required Type 2C elements" in error and "ReviewTime (300E,0005)" in error for error in result.errors)
    
    def test_approved_status_missing_reviewer_name_generates_error(self):
        """Test that APPROVED status missing ReviewerName generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240101"
        dataset.ReviewTime = "120000"
        # Missing ReviewerName
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Missing required Type 2C elements" in error and "ReviewerName (300E,0008)" in error for error in result.errors)
    
    def test_rejected_status_missing_all_review_info_generates_error(self):
        """Test that REJECTED status missing all review information generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.REJECTED.value
        # Missing all review information
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("Missing required Type 2C elements" in error and "REJECTED" in error for error in result.errors)
    
    def test_unapproved_status_with_review_info_generates_error(self):
        """Test that UNAPPROVED status with review information generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.UNAPPROVED.value
        dataset.ReviewDate = "20240101"
        dataset.ReviewTime = "120000"
        dataset.ReviewerName = "Dr. Smith"
        
        config = ValidationConfig(validate_conditional_requirements=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert not result.is_valid  # Error, not warning
        assert result.has_errors
        assert any("Type 2C elements present for UNAPPROVED status" in error for error in result.errors)
    
    def test_invalid_approval_status_generates_warning(self):
        """Test that invalid ApprovalStatus generates validation warning."""
        dataset = Dataset()
        dataset.ApprovalStatus = "INVALID_STATUS"
        
        config = ValidationConfig(check_enumerated_values=True)
        result = ApprovalValidator.validate(dataset, config)
        
        assert result.is_valid  # Warning, not error
        assert result.has_warnings
        assert any("ApprovalStatus (300E,0002)" in warning and "INVALID_STATUS" in warning for warning in result.warnings)
    
    def test_invalid_review_date_format_generates_error(self):
        """Test that invalid ReviewDate format generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "2024-01-01"  # Invalid format (should be YYYYMMDD)
        dataset.ReviewTime = "120000"
        dataset.ReviewerName = "Dr. Smith"
        
        result = ApprovalValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("ReviewDate (300E,0004)" in error and "DICOM DA format" in error for error in result.errors)
    
    def test_invalid_review_time_format_generates_error(self):
        """Test that invalid ReviewTime format generates validation error."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240101"
        dataset.ReviewTime = "12:00:00"  # Invalid format (should be HHMMSS)
        dataset.ReviewerName = "Dr. Smith"
        
        result = ApprovalValidator.validate(dataset)
        
        assert not result.is_valid
        assert result.has_errors
        assert any("ReviewTime (300E,0005)" in error and "HHMMSS" in error for error in result.errors)
    
    def test_valid_review_time_with_fractional_seconds_passes_validation(self):
        """Test that valid ReviewTime with fractional seconds passes validation."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240101"
        dataset.ReviewTime = "120000.123456"  # Valid with fractional seconds
        dataset.ReviewerName = "Dr. Smith"
        
        result = ApprovalValidator.validate(dataset)
        
        assert result.is_valid
        assert not result.has_errors
        assert len(result.errors) == 0
    
    def test_validation_with_default_config(self):
        """Test validation behavior with default ValidationConfig."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        dataset.ReviewDate = "20240101"
        dataset.ReviewTime = "120000"
        dataset.ReviewerName = "Dr. Smith"
        
        # Test with None config (should use defaults)
        result = ApprovalValidator.validate(dataset, None)
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
    
    def test_validation_with_conditional_requirements_disabled(self):
        """Test validation behavior with conditional requirements disabled."""
        dataset = Dataset()
        dataset.ApprovalStatus = ApprovalStatus.APPROVED.value
        # Missing review information - should be ignored with disabled config
        
        config = ValidationConfig(validate_conditional_requirements=False)
        result = ApprovalValidator.validate(dataset, config)
        
        assert result.is_valid  # Should not check conditional requirements
        assert not result.has_errors
    
    def test_validation_with_enumerated_values_disabled(self):
        """Test validation behavior with enumerated value checking disabled."""
        dataset = Dataset()
        dataset.ApprovalStatus = "INVALID_STATUS"
        
        config = ValidationConfig(check_enumerated_values=False)
        result = ApprovalValidator.validate(dataset, config)
        
        assert result.is_valid  # Should not check enumerated values
        assert not result.has_warnings
    
    def test_error_messages_include_dicom_tag_references(self):
        """Test that error messages include specific DICOM tag references."""
        dataset = Dataset()
        # Missing ApprovalStatus
        
        result = ApprovalValidator.validate(dataset)
        
        assert result.has_errors
        assert any("(300E,0002)" in error for error in result.errors)
    
    def test_multiple_validation_issues_are_all_reported(self):
        """Test that multiple validation issues are all captured and reported."""
        dataset = Dataset()
        dataset.ApprovalStatus = "INVALID_STATUS"  # Warning: invalid enum
        dataset.ReviewDate = "invalid-date"  # Error: invalid format
        dataset.ReviewTime = "invalid-time"  # Error: invalid format
        
        config = ValidationConfig(
            check_enumerated_values=True,
            validate_conditional_requirements=True
        )
        result = ApprovalValidator.validate(dataset, config)
        
        # Should have multiple issues reported
        assert result.has_errors or result.has_warnings
        assert result.total_issues >= 3  # At least 1 warning + 2 errors
    
    def test_integration_with_approval_module(self):
        """Test validator integration with the new composition-based ApprovalModule."""
        from pyrt_dicom.modules import ApprovalModule
        
        # Create module using new composition pattern
        approval = ApprovalModule.from_required_elements(
            approval_status=ApprovalStatus.APPROVED
        ).with_review_information(
            review_date="20240101",
            review_time="120000",
            reviewer_name="Dr. Smith"
        )
        
        # Test that module.validate() works correctly
        result = approval.validate()
        
        assert isinstance(result, ValidationResult)
        assert result.is_valid
        assert not result.has_errors
        
        # Test that validator.validate(module._dataset) also works
        direct_result = ApprovalValidator.validate(approval._dataset)
        
        assert isinstance(direct_result, ValidationResult)
        assert direct_result.is_valid
        assert not direct_result.has_errors
        
        # Both approaches should give same results
        assert result.is_valid == direct_result.is_valid
        assert len(result.errors) == len(direct_result.errors)
        assert len(result.warnings) == len(direct_result.warnings)