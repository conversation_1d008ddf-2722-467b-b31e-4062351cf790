"""
Test CineModule functionality.

CineModule implements DICOM PS3.3 C.7.6.5 Cine Module.
Describes a Multi-frame Cine Image.
"""

from pyrt_dicom.modules import CineModule
from pyrt_dicom.enums.image_enums import PreferredPlaybackSequencing, ChannelMode
from pyrt_dicom.validators import ValidationResult


class TestCineModule:
    """Test CineModule functionality."""
    
    def test_frame_time_operations(self):
        """Test frame time operations."""
        # Test with frame time
        cine = CineModule.from_required_elements().with_frame_time(33.33)
        
        dataset = cine.to_dataset()
        assert hasattr(dataset, 'FrameTime')
        assert dataset.FrameTime == 33.33
        assert cine.uses_frame_time is True
        assert cine.uses_frame_time_vector is False
    
    def test_frame_time_vector_operations(self):
        """Test frame time vector operations."""
        frame_vector = [0, 33.33, 33.33, 33.33, 33.33]
        cine = CineModule.from_required_elements().with_frame_time_vector(frame_vector)
        
        dataset = cine.to_dataset()
        assert hasattr(dataset, 'FrameTimeVector')
        assert dataset.FrameTimeVector == frame_vector
        assert cine.uses_frame_time is False
        assert cine.uses_frame_time_vector is True
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        cine = (CineModule.from_required_elements()
                .with_frame_time(33.33)
                .with_optional_elements(
                    preferred_playback_sequencing=PreferredPlaybackSequencing.LOOPING,
                    recommended_display_frame_rate=30,
                    cine_rate=30,
                    start_trim=1,
                    stop_trim=100,
                    frame_delay=10.0,
                    image_trigger_delay=5.0,
                    effective_duration=3.33,
                    actual_frame_duration=33.33
                ))
        
        dataset = cine.to_dataset()
        assert dataset.PreferredPlaybackSequencing == 0  # LOOPING enum value
        assert dataset.RecommendedDisplayFrameRate == 30
        assert dataset.CineRate == 30
        assert dataset.StartTrim == 1
        assert dataset.StopTrim == 100
        assert dataset.FrameDelay == 10.0
        assert dataset.ImageTriggerDelay == 5.0
        assert dataset.EffectiveDuration == 3.33
        assert dataset.ActualFrameDuration == 33.33
    
    def test_with_audio_channels(self):
        """Test adding audio channels."""
        audio_channel = CineModule.create_audio_channel_item(
            channel_id=1,
            channel_mode=ChannelMode.MONO,
            source_code_value='MAIN',
            source_coding_scheme='DCM'
        )
        
        cine = (CineModule.from_required_elements()
                .with_frame_time(33.33)
                .with_audio_channels([audio_channel], transfer_syntax_has_audio=True))
        
        assert cine.has_audio_channels is True
        assert cine.audio_channel_count == 1
        dataset = cine.to_dataset()
        assert hasattr(dataset, 'MultiplexedAudioChannelsDescriptionCodeSequence')
    
    def test_create_audio_channel_item(self):
        """Test creating audio channel sequence items."""
        channel_item = CineModule.create_audio_channel_item(
            channel_id=1,
            channel_mode=ChannelMode.MONO,
            source_code_value="MAIN",
            source_coding_scheme="DCM",
            source_code_meaning="Main audio channel"
        )
        
        assert channel_item.ChannelIdentificationCode == 1
        assert channel_item.ChannelMode == "MONO"
        assert len(channel_item.ChannelSourceSequence) == 1
        assert channel_item.ChannelSourceSequence[0].CodeValue == "MAIN"
        assert channel_item.ChannelSourceSequence[0].CodingSchemeDesignator == "DCM"
        assert channel_item.ChannelSourceSequence[0].CodeMeaning == "Main audio channel"
    
    def test_effective_frame_rate_calculation(self):
        """Test effective frame rate calculation from various sources."""
        # Test with CineRate
        cine = (CineModule.from_required_elements()
                .with_frame_time(33.33)
                .with_optional_elements(cine_rate=30))
        assert cine.effective_frame_rate == 30
        
        # Test with RecommendedDisplayFrameRate (when CineRate not present)
        cine = (CineModule.from_required_elements()
                .with_frame_time(33.33)
                .with_optional_elements(recommended_display_frame_rate=25))
        assert cine.effective_frame_rate == 25
        
        # Test with FrameTime (when others not present)
        cine = CineModule.from_required_elements().with_frame_time(40.0)  # 25 fps
        assert cine.effective_frame_rate is not None
        assert abs(cine.effective_frame_rate - 25.0) < 0.1            
        
        # Test with no timing information
        cine = CineModule()
        assert cine.effective_frame_rate is None
    
    def test_calculate_frame_relative_time(self):
        """Test frame relative time calculation."""
        # Test with frame time
        cine = (CineModule.from_required_elements()
                .with_frame_time(33.33)
                .with_optional_elements(frame_delay=10.0))
        
        # Frame 1: delay + time * (1-1) = 10.0 + 33.33 * 0 = 10.0
        assert cine.calculate_frame_relative_time(1) == 10.0
        # Frame 2: delay + time * (2-1) = 10.0 + 33.33 * 1 = 43.33
        assert cine.calculate_frame_relative_time(2) == 43.33
        # Frame 3: delay + time * (3-1) = 10.0 + 33.33 * 2 = 76.66
        assert cine.calculate_frame_relative_time(3) == 76.66
        
        # Test with frame time vector
        frame_vector = [0, 33.33, 33.33, 50.0]
        cine = (CineModule.from_required_elements()
                .with_frame_time_vector(frame_vector)
                .with_optional_elements(frame_delay=5.0))
        
        # Frame 1: delay + sum([0]) = 5.0 + 0 = 5.0
        assert cine.calculate_frame_relative_time(1) == 5.0
        # Frame 2: delay + sum([0, 33.33]) = 5.0 + 33.33 = 38.33
        assert cine.calculate_frame_relative_time(2) == 38.33
        # Frame 3: delay + sum([0, 33.33, 33.33]) = 5.0 + 66.66 = 71.66
        assert cine.calculate_frame_relative_time(3) == 71.66
        
        # Test invalid frame number
        assert cine.calculate_frame_relative_time(0) is None
        assert cine.calculate_frame_relative_time(10) is None  # Beyond vector length
    
    def test_preferred_playback_sequencing_enum(self):
        """Test preferred playback sequencing enum handling."""
        # Test with enum value
        cine = (CineModule.from_required_elements()
                .with_frame_time(33.33)
                .with_optional_elements(
                    preferred_playback_sequencing=PreferredPlaybackSequencing.SWEEPING
                ))
        dataset = cine.to_dataset()
        assert dataset.PreferredPlaybackSequencing == 1  # SWEEPING enum value
        
        # Test with integer value
        cine = (CineModule.from_required_elements()
                .with_frame_time(33.33)
                .with_optional_elements(preferred_playback_sequencing=0))
        dataset = cine.to_dataset()
        assert dataset.PreferredPlaybackSequencing == 0
    
    def test_to_dataset_generation(self):
        """Test that to_dataset() generates correct DICOM datasets."""
        cine = (CineModule.from_required_elements()
                .with_frame_time(33.33)
                .with_optional_elements(
                    preferred_playback_sequencing=PreferredPlaybackSequencing.LOOPING,
                    cine_rate=30.0,
                    start_trim=1,
                    stop_trim=100
                ))
        
        dataset = cine.to_dataset()
        
        # Verify dataset type and contents
        from pydicom import Dataset
        assert isinstance(dataset, Dataset)
        assert len(dataset) > 0
        
        # Verify all elements are present
        assert hasattr(dataset, 'FrameTime')
        assert hasattr(dataset, 'PreferredPlaybackSequencing')
        assert hasattr(dataset, 'CineRate')
        assert hasattr(dataset, 'StartTrim')
        assert hasattr(dataset, 'StopTrim')
        
        # Verify values are correct
        assert dataset.FrameTime == 33.33
        assert dataset.PreferredPlaybackSequencing == 0  # LOOPING
        assert dataset.CineRate == 30.0
        assert dataset.StartTrim == 1
        assert dataset.StopTrim == 100
    
    def test_audio_channel_properties(self):
        """Test audio channel-related properties."""
        # Test without audio channels
        cine = CineModule.from_required_elements().with_frame_time(33.33)
        assert cine.has_audio_channels is False
        assert cine.audio_channel_count == 0
        
        # Test with multiple audio channels
        audio_channels = [
            CineModule.create_audio_channel_item(
                channel_id=1,
                channel_mode=ChannelMode.MONO,
                source_code_value='MAIN',
                source_coding_scheme='DCM'
            ),
            CineModule.create_audio_channel_item(
                channel_id=2,
                channel_mode=ChannelMode.STEREO,
                source_code_value='AUX',
                source_coding_scheme='DCM'
            )
        ]
        
        cine = (CineModule.from_required_elements()
                .with_frame_time(33.33)
                .with_audio_channels(audio_channels, transfer_syntax_has_audio=True))
        assert cine.has_audio_channels is True
        assert cine.audio_channel_count == 2
    
    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        cine = CineModule.from_required_elements().with_frame_time(33.33)
        assert hasattr(cine, 'validate')
        assert callable(cine.validate)
        
        # Test validation result structure
        validation_result = cine.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_channel_mode_enum_handling(self):
        """Test channel mode enum handling in audio channels."""
        # Test with enum value
        channel_item = CineModule.create_audio_channel_item(
            channel_id=1,
            channel_mode=ChannelMode.STEREO,
            source_code_value="MAIN"
        )
        assert channel_item.ChannelMode == "STEREO"
        
        # Test with string value
        channel_item = CineModule.create_audio_channel_item(
            channel_id=1,
            channel_mode="MONO",
            source_code_value="MAIN"
        )
        assert channel_item.ChannelMode == "MONO"
    
    def test_frame_time_zero_handling(self):
        """Test handling of zero frame time."""
        cine = CineModule.from_required_elements().with_frame_time(0.0)
        assert cine.effective_frame_rate is None  # Should handle division by zero
        
        # Test calculation with zero frame time
        assert cine.calculate_frame_relative_time(1) == 0.0
        assert cine.calculate_frame_relative_time(2) == 0.0
