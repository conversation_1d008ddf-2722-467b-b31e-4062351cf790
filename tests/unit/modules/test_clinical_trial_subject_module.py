"""
Test ClinicalTrialSubjectModule functionality.

ClinicalTrialSubjectModule implements DICOM PS3.3 C.7.1.3 Clinical Trial Subject Module.
Contains attributes that identify a Patient as a clinical trial or research Subject.
"""

from pyrt_dicom.modules import ClinicalTrialSubjectModule
import pytest
from pyrt_dicom.validators import ValidationResult


class TestClinicalTrialSubjectModule:
    """Test ClinicalTrialSubjectModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001",
            clinical_trial_protocol_name="Phase II Cancer Study",
            clinical_trial_site_id="SITE-001",
            clinical_trial_site_name="University Medical Center"
        )

        dataset = trial_subject.to_dataset()
        assert dataset.ClinicalTrialSponsorName == "ACME Research Corp"
        assert dataset.ClinicalTrialProtocolID == "TRIAL-2024-001"
        assert dataset.ClinicalTrialProtocolName == "Phase II Cancer Study"
        assert dataset.ClinicalTrialSiteID == "SITE-001"
        assert dataset.ClinicalTrialSiteName == "University Medical Center"
    
    def test_required_elements_validation(self):
        """Test validation of required elements."""
        # Test Type 1 (sponsor name and protocol ID) vs Type 2 (others can be empty)
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001",
            clinical_trial_protocol_name="",  # Type 2 - can be empty
            clinical_trial_site_id="",  # Type 2 - can be empty
            clinical_trial_site_name=""  # Type 2 - can be empty
        )

        dataset = trial_subject.to_dataset()
        assert dataset.ClinicalTrialSponsorName == "ACME Research Corp"
        assert dataset.ClinicalTrialProtocolID == "TRIAL-2024-001"
        assert dataset.ClinicalTrialProtocolName == ""
        assert dataset.ClinicalTrialSiteID == ""
        assert dataset.ClinicalTrialSiteName == ""
    
    def test_with_subject_identification_success(self):
        """Test successful subject identification addition."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001"
        )

        # Test with subject ID
        trial_subject.with_subject_identification(
            clinical_trial_subject_id="SUBJ-12345"
        )

        dataset = trial_subject.to_dataset()
        assert hasattr(dataset, 'ClinicalTrialSubjectID')
        assert dataset.ClinicalTrialSubjectID == "SUBJ-12345"
        assert trial_subject.has_subject_identification is True
    
    def test_with_subject_identification_reading_id(self):
        """Test subject identification with reading ID."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001"
        )

        # Test with reading ID instead of subject ID
        trial_subject.with_subject_identification(
            clinical_trial_subject_reading_id="READ-67890"
        )

        dataset = trial_subject.to_dataset()
        assert hasattr(dataset, 'ClinicalTrialSubjectReadingID')
        assert dataset.ClinicalTrialSubjectReadingID == "READ-67890"
        assert trial_subject.has_subject_identification is True
    
    def test_with_subject_identification_both_ids(self):
        """Test subject identification with both IDs."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001"
        )

        # Test with both IDs
        trial_subject.with_subject_identification(
            clinical_trial_subject_id="SUBJ-12345",
            clinical_trial_subject_reading_id="READ-67890"
        )

        dataset = trial_subject.to_dataset()
        assert dataset.ClinicalTrialSubjectID == "SUBJ-12345"
        assert dataset.ClinicalTrialSubjectReadingID == "READ-67890"
        assert trial_subject.has_subject_identification is True
    
    def test_with_subject_identification_failure(self):
        """Test subject identification failure when neither ID provided."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001"
        )
        
        # Should raise ValueError when neither ID is provided
        with pytest.raises(ValueError, match="Either clinical_trial_subject_id or clinical_trial_subject_reading_id is required"):
            trial_subject.with_subject_identification()
    
    def test_with_ethics_committee(self):
        """Test ethics committee information addition."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001"
        )

        trial_subject.with_ethics_committee(
            clinical_trial_protocol_ethics_committee_name="University IRB",
            clinical_trial_protocol_ethics_committee_approval_number="IRB-2024-001"
        )

        dataset = trial_subject.to_dataset()
        assert hasattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeName')
        assert hasattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeApprovalNumber')
        assert dataset.ClinicalTrialProtocolEthicsCommitteeName == "University IRB"
        assert dataset.ClinicalTrialProtocolEthicsCommitteeApprovalNumber == "IRB-2024-001"
        assert trial_subject.has_ethics_committee_info is True
    
    def test_with_optional_elements(self):
        """Test adding optional elements."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001"
        ).with_optional_elements(
            issuer_of_clinical_trial_protocol_id="FDA",
            issuer_of_clinical_trial_site_id="SPONSOR",
            issuer_of_clinical_trial_subject_id="HOSPITAL",
            clinical_trial_protocol_ethics_committee_approval_number="IRB-2024-001"
        )

        dataset = trial_subject.to_dataset()
        assert hasattr(dataset, 'IssuerOfClinicalTrialProtocolID')
        assert hasattr(dataset, 'IssuerOfClinicalTrialSiteID')
        assert hasattr(dataset, 'IssuerOfClinicalTrialSubjectID')
        assert hasattr(dataset, 'ClinicalTrialProtocolEthicsCommitteeApprovalNumber')
        assert dataset.IssuerOfClinicalTrialProtocolID == "FDA"
        assert dataset.IssuerOfClinicalTrialSiteID == "SPONSOR"
        assert dataset.IssuerOfClinicalTrialSubjectID == "HOSPITAL"
    
    def test_create_other_protocol_id_item(self):
        """Test creation of other protocol ID sequence item."""
        item = ClinicalTrialSubjectModule.create_other_protocol_id_item(
            clinical_trial_protocol_id="ALT-PROTOCOL-001",
            issuer_of_clinical_trial_protocol_id="SECONDARY-SPONSOR"
        )
        
        assert hasattr(item, 'ClinicalTrialProtocolID')
        assert hasattr(item, 'IssuerOfClinicalTrialProtocolID')
        assert item.ClinicalTrialProtocolID == "ALT-PROTOCOL-001"
        assert item.IssuerOfClinicalTrialProtocolID == "SECONDARY-SPONSOR"
    
    def test_property_has_subject_identification(self):
        """Test has_subject_identification property."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001"
        )
        
        # Initially should be False
        assert trial_subject.has_subject_identification is False
        
        # After adding subject ID
        trial_subject.with_subject_identification(clinical_trial_subject_id="SUBJ-001")
        assert trial_subject.has_subject_identification is True
    
    def test_property_has_ethics_committee_info(self):
        """Test has_ethics_committee_info property."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001"
        )
        
        # Initially should be False
        assert trial_subject.has_ethics_committee_info is False
        
        # After adding ethics committee
        trial_subject.with_ethics_committee(
            clinical_trial_protocol_ethics_committee_name="University IRB"
        )
        assert trial_subject.has_ethics_committee_info is True
    
    def test_property_has_additional_protocol_ids(self):
        """Test has_additional_protocol_ids property."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001"
        )
        
        # Initially should be False
        assert trial_subject.has_additional_protocol_ids is False
        
        # After adding other protocol IDs sequence
        other_ids = [ClinicalTrialSubjectModule.create_other_protocol_id_item(
            "ALT-001", "SECONDARY"
        )]
        trial_subject.with_optional_elements(
            other_clinical_trial_protocol_ids_sequence=other_ids
        )
        assert trial_subject.has_additional_protocol_ids is True
    
    def test_to_dataset_method(self):
        """Test that to_dataset method generates correct DICOM dataset."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001",
            clinical_trial_protocol_name="Phase II Cancer Study",
            clinical_trial_site_id="SITE-001",
            clinical_trial_site_name="University Medical Center"
        ).with_subject_identification(
            clinical_trial_subject_id="SUBJ-12345"
        )

        dataset = trial_subject.to_dataset()

        # Test that dataset is a pydicom Dataset
        import pydicom
        assert isinstance(dataset, pydicom.Dataset)

        # Test that all data is present
        assert len(dataset) > 0
        assert hasattr(dataset, 'ClinicalTrialSponsorName')
        assert hasattr(dataset, 'ClinicalTrialProtocolID')
        assert hasattr(dataset, 'ClinicalTrialSubjectID')

        # Test that values are correct
        assert dataset.ClinicalTrialSponsorName == "ACME Research Corp"
        assert dataset.ClinicalTrialProtocolID == "TRIAL-2024-001"
        assert dataset.ClinicalTrialSubjectID == "SUBJ-12345"

    def test_validation_method_exists(self):
        """Test that validation method exists and is callable."""
        trial_subject = ClinicalTrialSubjectModule.from_required_elements(
            clinical_trial_sponsor_name="ACME Research Corp",
            clinical_trial_protocol_id="TRIAL-2024-001"
        )

        assert hasattr(trial_subject, 'validate')
        assert callable(trial_subject.validate)

        # Test validation result structure
        validation_result = trial_subject.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)