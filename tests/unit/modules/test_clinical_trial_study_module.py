"""
Test ClinicalTrialStudyModule functionality.

ClinicalTrialStudyModule implements DICOM PS3.3 C.7.2.3 Clinical Trial Study Module.
Contains attributes that identify a Study in the context of a clinical trial or research.
"""

import pydicom
from pyrt_dicom.modules import ClinicalTrialStudyModule
from pyrt_dicom.enums.clinical_trial_enums import ConsentForDistribution, DistributionType, LongitudinalTemporalEventType
from pyrt_dicom.validators import ValidationResult


class TestClinicalTrialStudyModule:
    """Test ClinicalTrialStudyModule functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        trial_study = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id="TP001"
        )

        dataset = trial_study.to_dataset()
        assert dataset.ClinicalTrialTimePointID == "TP001"
    
    def test_from_required_elements_default(self):
        """Test creation with default empty value (Type 2)."""
        trial_study = ClinicalTrialStudyModule.from_required_elements()

        # Type 2 element can be empty but must be present
        dataset = trial_study.to_dataset()
        assert dataset.ClinicalTrialTimePointID == ""
    
    def test_with_optional_elements(self):
        """Test addition of optional elements."""
        trial_study = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id="TP001"
        ).with_optional_elements(
            issuer_of_clinical_trial_time_point_id="TRIAL_ORG",
            clinical_trial_time_point_description="Baseline imaging",
            longitudinal_temporal_offset_from_event=0.0
        )

        dataset = trial_study.to_dataset()
        assert dataset.IssuerOfClinicalTrialTimePointID == "TRIAL_ORG"
        assert dataset.ClinicalTrialTimePointDescription == "Baseline imaging"
        assert dataset.LongitudinalTemporalOffsetFromEvent == 0.0
    
    def test_with_temporal_event(self):
        """Test temporal event information addition."""
        trial_study = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id="TP001"
        ).with_temporal_event(
            longitudinal_temporal_offset_from_event=30.5,
            longitudinal_temporal_event_type=LongitudinalTemporalEventType.ENROLLMENT
        )

        dataset = trial_study.to_dataset()
        assert dataset.LongitudinalTemporalOffsetFromEvent == 30.5
        assert dataset.LongitudinalTemporalEventType == "ENROLLMENT"

    def test_to_dataset_method(self):
        """Test dataset generation from module."""
        trial_study = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id="TP001"
        ).with_optional_elements(
            clinical_trial_time_point_description="Baseline imaging"
        )

        dataset = trial_study.to_dataset()
        assert isinstance(dataset, pydicom.Dataset)
        assert len(dataset) > 0
        assert dataset.ClinicalTrialTimePointID == "TP001"
        assert dataset.ClinicalTrialTimePointDescription == "Baseline imaging"

    def test_create_consent_item_basic(self):
        """Test creation of basic consent item."""
        consent_item = ClinicalTrialStudyModule.create_consent_item(
            consent_for_distribution_flag=ConsentForDistribution.NO
        )
        
        assert consent_item.ConsentForDistributionFlag == "NO"
        # Should not have distribution type when consent is NO
        assert not hasattr(consent_item, 'DistributionType')
    
    def test_create_consent_item_with_distribution(self):
        """Test creation of consent item with distribution type."""
        consent_item = ClinicalTrialStudyModule.create_consent_item(
            consent_for_distribution_flag=ConsentForDistribution.YES,
            distribution_type=DistributionType.NAMED_PROTOCOL,
            clinical_trial_protocol_id="PROTOCOL_001"
        )
        
        assert consent_item.ConsentForDistributionFlag == "YES"
        assert consent_item.DistributionType == "NAMED_PROTOCOL"
        assert consent_item.ClinicalTrialProtocolID == "PROTOCOL_001"
    
    def test_create_time_point_type_code_item(self):
        """Test creation of time point type code item."""
        code_item = ClinicalTrialStudyModule.create_time_point_type_code_item(
            code_value="T1",
            coding_scheme_designator="LOCAL",
            code_meaning="Time Point 1"
        )
        
        assert code_item.CodeValue == "T1"
        assert code_item.CodingSchemeDesignator == "LOCAL"
        assert code_item.CodeMeaning == "Time Point 1"
    
    def test_has_temporal_event_info(self):
        """Test temporal event information detection."""
        trial_study = ClinicalTrialStudyModule.from_required_elements()
        assert not trial_study.has_temporal_event_info
        
        trial_study.with_temporal_event(
            longitudinal_temporal_offset_from_event=0.0,
            longitudinal_temporal_event_type=LongitudinalTemporalEventType.BASELINE
        )
        assert trial_study.has_temporal_event_info
    
    def test_has_consent_info(self):
        """Test consent information detection."""
        trial_study = ClinicalTrialStudyModule.from_required_elements()
        assert not trial_study.has_consent_info
        
        consent_item = ClinicalTrialStudyModule.create_consent_item(
            consent_for_distribution_flag=ConsentForDistribution.NO
        )
        trial_study.with_consent_for_clinical_trial_use([consent_item])
        assert trial_study.has_consent_info
    
    def test_has_time_point_description(self):
        """Test time point description detection."""
        trial_study = ClinicalTrialStudyModule.from_required_elements()
        assert not trial_study.has_time_point_description
        
        trial_study.with_optional_elements(
            clinical_trial_time_point_description="Follow-up imaging"
        )
        assert trial_study.has_time_point_description
    
    def test_validate_method(self):
        """Test validation method exists and returns expected structure."""
        trial_study = ClinicalTrialStudyModule.from_required_elements(
            clinical_trial_time_point_id="TP001"
        )
        
        assert hasattr(trial_study, 'validate')
        assert callable(trial_study.validate)
        
        # Test validation result structure
        validation_result = trial_study.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)