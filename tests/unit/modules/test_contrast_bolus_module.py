"""
Test ContrastBolusModule functionality.

ContrastBolusModule implements DICOM PS3.3 C.7.6.4 Contrast/Bolus Module.
Contains attributes that describe the contrast/bolus agent used in the acquisition.
Tests the composition-based architecture with internal dataset management.
"""

import pydicom
from pyrt_dicom.modules import ContrastBolusModule
from pyrt_dicom.enums.image_enums import ContrastBolusIngredient
from pyrt_dicom.validators import ValidationResult


class TestContrastBolusModule:
    """Test ContrastBolusModule basic functionality."""
    
    def test_from_required_elements_success(self):
        """Test successful creation with required elements."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        )
        
        dataset = contrast_bolus.to_dataset()
        assert dataset.ContrastBolusAgent == "Iodinated contrast agent"
    
    def test_from_required_elements_empty_defaults(self):
        """Test creation with default empty values (Type 2 elements)."""
        contrast_bolus = ContrastBolusModule.from_required_elements()
        
        dataset = contrast_bolus.to_dataset()
        # Type 2 elements can be empty but must be present
        assert dataset.ContrastBolusAgent == ""
    
    def test_with_optional_elements_basic(self):
        """Test adding basic optional elements."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_route="INTRAVENOUS",
            contrast_bolus_volume=100.0,
            contrast_bolus_total_dose=50.0
        )
        
        dataset = contrast_bolus.to_dataset()
        assert dataset.ContrastBolusRoute == "INTRAVENOUS"
        assert dataset.ContrastBolusVolume == 100.0
        assert dataset.ContrastBolusTotalDose == 50.0
    
    def test_with_optional_elements_timing(self):
        """Test adding timing-related optional elements."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_start_time="120000",
            contrast_bolus_stop_time="120030"
        )
        
        dataset = contrast_bolus.to_dataset()
        assert dataset.ContrastBolusStartTime == "120000"
        assert dataset.ContrastBolusStopTime == "120030"
    
    def test_with_optional_elements_flow_data(self):
        """Test adding flow rate and duration data."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_flow_rate=[5.0, 3.0],
            contrast_flow_duration=[20.0, 10.0]
        )
        
        dataset = contrast_bolus.to_dataset()
        assert dataset.ContrastFlowRate == [5.0, 3.0]
        assert dataset.ContrastFlowDuration == [20.0, 10.0]
    
    def test_with_optional_elements_ingredient(self):
        """Test adding ingredient information with enum."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE,
            contrast_bolus_ingredient_concentration=370.0
        )
        
        dataset = contrast_bolus.to_dataset()
        assert dataset.ContrastBolusIngredient == "IODINE"
        assert dataset.ContrastBolusIngredientConcentration == 370.0
    
    def test_create_contrast_agent_code_item(self):
        """Test creating contrast agent code item."""
        code_item = ContrastBolusModule.create_contrast_agent_code_item(
            code_value="C-B0322",
            coding_scheme_designator="SRT",
            code_meaning="Iodinated contrast agent"
        )
        
        assert code_item.CodeValue == "C-B0322"
        assert code_item.CodingSchemeDesignator == "SRT"
        assert code_item.CodeMeaning == "Iodinated contrast agent"
    
    def test_create_administration_route_code_item(self):
        """Test creating administration route code item."""
        code_item = ContrastBolusModule.create_administration_route_code_item(
            code_value="47625008",
            coding_scheme_designator="SCT",
            code_meaning="Intravenous route"
        )
        
        assert code_item.CodeValue == "47625008"
        assert code_item.CodingSchemeDesignator == "SCT"
        assert code_item.CodeMeaning == "Intravenous route"
    
    def test_create_additional_drug_code_item(self):
        """Test creating additional drug code item."""
        code_item = ContrastBolusModule.create_additional_drug_code_item(
            code_value="387467008",
            coding_scheme_designator="SCT",
            code_meaning="Saline"
        )
        
        assert code_item.CodeValue == "387467008"
        assert code_item.CodingSchemeDesignator == "SCT"
        assert code_item.CodeMeaning == "Saline"
    
    def test_property_has_contrast_agent(self):
        """Test has_contrast_agent property."""
        # With agent
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        )
        assert contrast_bolus.has_contrast_agent is True
        
        # Without agent (empty)
        contrast_bolus_empty = ContrastBolusModule.from_required_elements()
        assert contrast_bolus_empty.has_contrast_agent is False
    
    def test_property_has_timing_information(self):
        """Test has_timing_information property."""
        # Without timing info
        contrast_bolus = ContrastBolusModule.from_required_elements()
        assert contrast_bolus.has_timing_information is False
        
        # With start time
        contrast_bolus.with_optional_elements(contrast_bolus_start_time="120000")
        assert contrast_bolus.has_timing_information is True
    
    def test_property_has_volume_information(self):
        """Test has_volume_information property."""
        # Without volume info
        contrast_bolus = ContrastBolusModule.from_required_elements()
        assert contrast_bolus.has_volume_information is False
        
        # With volume
        contrast_bolus.with_optional_elements(contrast_bolus_volume=100.0)
        assert contrast_bolus.has_volume_information is True
    
    def test_property_has_ingredient_information(self):
        """Test has_ingredient_information property."""
        # Without ingredient info
        contrast_bolus = ContrastBolusModule.from_required_elements()
        assert contrast_bolus.has_ingredient_information is False
        
        # With ingredient
        contrast_bolus.with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE
        )
        assert contrast_bolus.has_ingredient_information is True
    
    def test_property_is_iodine_based(self):
        """Test is_iodine_based property."""
        contrast_bolus = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE
        )
        assert contrast_bolus.is_iodine_based is True
        
        contrast_bolus_gd = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.GADOLINIUM
        )
        assert contrast_bolus_gd.is_iodine_based is False
    
    def test_property_is_gadolinium_based(self):
        """Test is_gadolinium_based property."""
        contrast_bolus = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.GADOLINIUM
        )
        assert contrast_bolus.is_gadolinium_based is True
        
        contrast_bolus_iodine = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE
        )
        assert contrast_bolus_iodine.is_gadolinium_based is False
    
    def test_validate_method_exists(self):
        """Test that validate method exists and returns expected structure."""
        contrast_bolus = ContrastBolusModule.from_required_elements()
        
        assert hasattr(contrast_bolus, 'validate')
        assert callable(contrast_bolus.validate)
        
        # Test validation result structure
        validation_result = contrast_bolus.validate()
        assert validation_result is not None
        assert isinstance(validation_result, ValidationResult)
        assert hasattr(validation_result, 'errors')
        assert hasattr(validation_result, 'warnings')
        assert isinstance(validation_result.errors, list)
        assert isinstance(validation_result.warnings, list)
    
    def test_to_dataset_generation(self):
        """Test dataset generation functionality."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_route="INTRAVENOUS",
            contrast_bolus_volume=100.0,
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE
        )
        
        dataset = contrast_bolus.to_dataset()
        
        # Verify dataset is correct type and contains expected elements
        assert isinstance(dataset, pydicom.Dataset)
        assert hasattr(dataset, 'ContrastBolusAgent')
        assert hasattr(dataset, 'ContrastBolusRoute')
        assert hasattr(dataset, 'ContrastBolusVolume')
        assert hasattr(dataset, 'ContrastBolusIngredient')
        
        # Verify values are correct
        assert dataset.ContrastBolusAgent == "Iodinated contrast agent"
        assert dataset.ContrastBolusRoute == "INTRAVENOUS"
        assert dataset.ContrastBolusVolume == 100.0
        assert dataset.ContrastBolusIngredient == "IODINE"
    
    def test_to_dataset_fresh_copy(self):
        """Test that to_dataset() returns a fresh copy each time."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Test Agent"
        )
        
        dataset1 = contrast_bolus.to_dataset()
        dataset2 = contrast_bolus.to_dataset()
        
        # Should be different objects but with same content
        assert dataset1 is not dataset2
        assert dataset1.ContrastBolusAgent == dataset2.ContrastBolusAgent
    
    def test_module_properties(self):
        """Test inherited BaseModule properties."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Test Agent"
        )
        
        # Test module name
        assert contrast_bolus.module_name == "ContrastBolusModule"
        
        # Test has_data property
        assert contrast_bolus.has_data is True
        
        # Test element count
        assert contrast_bolus.get_element_count() == 1  # Only ContrastBolusAgent
        
        # Add more elements and test count
        contrast_bolus.with_optional_elements(
            contrast_bolus_route="INTRAVENOUS",
            contrast_bolus_volume=100.0
        )
        assert contrast_bolus.get_element_count() == 3
    
    def test_string_representation(self):
        """Test string representation of module."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Test Agent"
        )
        
        repr_str = repr(contrast_bolus)
        assert "ContrastBolusModule" in repr_str
        assert "1 attribute" in repr_str


class TestContrastBolusModuleValidation:
    """Test ContrastBolusModule validation and conditional logic."""
    
    def test_flow_rate_duration_consistency_validation(self):
        """Test validation of flow rate/duration correspondence."""
        import pytest
        
        contrast_bolus = ContrastBolusModule.from_required_elements()
        
        # Should raise error when counts don't match
        with pytest.raises(ValueError, match="must have the same number of values"):
            contrast_bolus.with_optional_elements(
                contrast_flow_rate=[5.0, 3.0],  # 2 values
                contrast_flow_duration=[20.0]    # 1 value
            )
    
    def test_dose_volume_consistency_validation(self):
        """Test validation of dose/volume logical consistency."""
        import pytest
        
        contrast_bolus = ContrastBolusModule.from_required_elements()
        
        # Should raise error when total dose exceeds volume
        with pytest.raises(ValueError, match="cannot exceed"):
            contrast_bolus.with_optional_elements(
                contrast_bolus_volume=50.0,      # 50ml volume
                contrast_bolus_total_dose=100.0   # 100ml dose > volume
            )
    
    def test_administration_route_single_item_validation(self):
        """Test validation of single item constraint for administration route."""
        import pytest
        from pydicom import Dataset
        
        contrast_bolus = ContrastBolusModule.from_required_elements()
        
        # Create multiple route items
        route1 = Dataset()
        route1.CodeValue = "47625008"
        route1.CodingSchemeDesignator = "SCT"
        
        route2 = Dataset()
        route2.CodeValue = "12345678"
        route2.CodingSchemeDesignator = "SCT"
        
        # Should raise error with multiple items
        with pytest.raises(ValueError, match="should contain only a single Item"):
            contrast_bolus.with_administration_route(
                administration_route_sequence=[route1, route2]
            )
    
    def test_flow_rate_duration_consistency_properties(self):
        """Test consistency checking properties."""
        contrast_bolus = ContrastBolusModule.from_required_elements()
        
        # Both absent - should be consistent
        assert contrast_bolus.has_flow_rate_duration_consistency is True
        
        # Only flow rate present - should be consistent
        contrast_bolus.with_optional_elements(contrast_flow_rate=[5.0])
        assert contrast_bolus.has_flow_rate_duration_consistency is True
        
        # Both present with matching counts - should be consistent
        contrast_bolus2 = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_flow_rate=[5.0, 3.0],
            contrast_flow_duration=[20.0, 10.0]
        )
        assert contrast_bolus2.has_flow_rate_duration_consistency is True
    
    def test_dose_volume_consistency_properties(self):
        """Test dose/volume consistency checking properties."""
        contrast_bolus = ContrastBolusModule.from_required_elements()
        
        # Both absent - should be consistent
        assert contrast_bolus.has_dose_volume_consistency is True
        
        # Valid relationship - total dose <= volume
        contrast_bolus.with_optional_elements(
            contrast_bolus_volume=100.0,
            contrast_bolus_total_dose=50.0
        )
        assert contrast_bolus.has_dose_volume_consistency is True
    
    def test_ingredient_concentration_pairing_properties(self):
        """Test ingredient/concentration pairing properties."""
        contrast_bolus = ContrastBolusModule.from_required_elements()
        
        # Both absent - should be appropriately paired
        assert contrast_bolus.has_ingredient_concentration_pairing is True
        
        # Both present - should be appropriately paired  
        contrast_bolus.with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE,
            contrast_bolus_ingredient_concentration=370.0
        )
        assert contrast_bolus.has_ingredient_concentration_pairing is True
        
        # Only one present - less ideal but handled gracefully
        contrast_bolus2 = ContrastBolusModule.from_required_elements().with_optional_elements(
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE
        )
        assert contrast_bolus2.has_ingredient_concentration_pairing is False


class TestContrastBolusModuleConditionalLogic:
    """Test ContrastBolusModule conditional logic and sequence handling."""
    
    def test_agent_sequence_with_code_macro(self):
        """Test contrast agent sequence with code sequence macro."""
        agent_code_item = ContrastBolusModule.create_contrast_agent_code_item(
            code_value="C-B0322",
            coding_scheme_designator="SRT", 
            code_meaning="Iodinated contrast agent"
        )
        
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_bolus_agent_sequence=[agent_code_item]
        )
        
        dataset = contrast_bolus.to_dataset()
        assert hasattr(dataset, 'ContrastBolusAgentSequence')
        assert len(dataset.ContrastBolusAgentSequence) == 1
        assert dataset.ContrastBolusAgentSequence[0].CodeValue == "C-B0322"
    
    def test_administration_route_with_additional_drugs(self):
        """Test administration route sequence with nested additional drug sequence."""
        route_item = ContrastBolusModule.create_administration_route_code_item(
            code_value="47625008",
            coding_scheme_designator="SCT",
            code_meaning="Intravenous route"
        )
        
        drug_item = ContrastBolusModule.create_additional_drug_code_item(
            code_value="387467008",
            coding_scheme_designator="SCT",
            code_meaning="Saline"
        )
        
        contrast_bolus = ContrastBolusModule.from_required_elements().with_administration_route(
            administration_route_sequence=[route_item],
            additional_drug_sequence=[drug_item]
        )
        
        dataset = contrast_bolus.to_dataset()
        assert hasattr(dataset, 'ContrastBolusAdministrationRouteSequence')
        assert len(dataset.ContrastBolusAdministrationRouteSequence) == 1
        
        route = dataset.ContrastBolusAdministrationRouteSequence[0]
        assert route.CodeValue == "47625008"
        assert hasattr(route, 'AdditionalDrugSequence')
        assert len(route.AdditionalDrugSequence) == 1
        assert route.AdditionalDrugSequence[0].CodeValue == "387467008"
    
    def test_stepped_injection_protocol(self):
        """Test stepped injection with multiple flow rates and durations."""
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iodinated contrast agent"
        ).with_optional_elements(
            contrast_flow_rate=[5.0, 3.0, 1.0],      # Three injection rates
            contrast_flow_duration=[10.0, 15.0, 5.0]  # Corresponding durations
        )
        
        dataset = contrast_bolus.to_dataset()
        assert dataset.ContrastFlowRate == [5.0, 3.0, 1.0]
        assert dataset.ContrastFlowDuration == [10.0, 15.0, 5.0]
        
        # Verify consistency properties
        assert contrast_bolus.get_flow_rate_count() == 3
        assert contrast_bolus.get_flow_duration_count() == 3
        assert contrast_bolus.has_flow_rate_duration_consistency is True
    
    def test_dilution_calculation_example(self):
        """Test dilution calculation example from DICOM standard."""
        # Example: 100ml injection of 76% Diatrizoate diluted 1:1
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="76% Diatrizoate"
        ).with_optional_elements(
            contrast_bolus_volume=100.0,                            # 100ml diluted volume
            contrast_bolus_total_dose=50.0,                         # 50ml undiluted
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE,
            contrast_bolus_ingredient_concentration=370.0            # 370mg/ml
        )
        
        dataset = contrast_bolus.to_dataset()
        assert dataset.ContrastBolusAgent == "76% Diatrizoate"
        assert dataset.ContrastBolusVolume == 100.0
        assert dataset.ContrastBolusTotalDose == 50.0
        assert dataset.ContrastBolusIngredient == "IODINE"
        assert dataset.ContrastBolusIngredientConcentration == 370.0
        
        # Verify logical consistency
        assert contrast_bolus.has_dose_volume_consistency is True
        assert contrast_bolus.is_iodine_based is True
        assert contrast_bolus.has_ingredient_concentration_pairing is True


class TestContrastBolusModuleIntegration:
    """Test ContrastBolusModule integration scenarios."""
    
    def test_complete_contrast_protocol_workflow(self):
        """Test complete contrast injection protocol workflow."""
        # Create comprehensive contrast protocol
        contrast_bolus = ContrastBolusModule.from_required_elements(
            contrast_bolus_agent="Iohexol 350 mg I/ml"
        ).with_optional_elements(
            contrast_bolus_route="INTRAVENOUS",
            contrast_bolus_volume=100.0,
            contrast_bolus_start_time="120000",
            contrast_bolus_stop_time="120030", 
            contrast_bolus_total_dose=100.0,
            contrast_flow_rate=[5.0, 2.0],
            contrast_flow_duration=[20.0, 10.0],
            contrast_bolus_ingredient=ContrastBolusIngredient.IODINE,
            contrast_bolus_ingredient_concentration=350.0
        )
        
        # Add coded sequences
        agent_code = ContrastBolusModule.create_contrast_agent_code_item(
            code_value="C-B0322",
            coding_scheme_designator="SRT",
            code_meaning="Iodinated contrast agent" 
        )
        
        route_code = ContrastBolusModule.create_administration_route_code_item(
            code_value="47625008",
            coding_scheme_designator="SCT",
            code_meaning="Intravenous route"
        )
        
        contrast_bolus.with_optional_elements(
            contrast_bolus_agent_sequence=[agent_code]
        ).with_administration_route(
            administration_route_sequence=[route_code]
        )
        
        # Validate complete protocol
        dataset = contrast_bolus.to_dataset()
        validation_result = contrast_bolus.validate()
        
        # Verify all components are present and valid
        assert dataset.ContrastBolusAgent == "Iohexol 350 mg I/ml"
        assert dataset.ContrastBolusRoute == "INTRAVENOUS"
        assert hasattr(dataset, 'ContrastBolusAgentSequence')
        assert hasattr(dataset, 'ContrastBolusAdministrationRouteSequence')
        assert contrast_bolus.has_timing_information is True
        assert contrast_bolus.has_volume_information is True
        assert contrast_bolus.has_ingredient_information is True
        assert contrast_bolus.has_flow_rate_duration_consistency is True
        assert contrast_bolus.has_dose_volume_consistency is True
        assert isinstance(validation_result, ValidationResult)